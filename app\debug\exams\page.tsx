'use client';

import { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

export default function ExamsDebugPage() {
  const [lessons, setLessons] = useState<any[]>([]);
  const [exams, setExams] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadData() {
      try {
        setLoading(true);
        console.log('جاري جلب البيانات من قاعدة البيانات...');

        // جلب جميع الدروس
        const { data: lessonsData, error: lessonsError } = await supabase
          .from('lessons')
          .select('*')
          .order('id');

        if (lessonsError) {
          console.error('خطأ في جلب الدروس:', lessonsError);
          setError('خطأ في جلب الدروس: ' + lessonsError.message);
          return;
        }

        console.log('الدروس المجلبة:', lessonsData);
        setLessons(lessonsData || []);

        // جلب جميع الامتحانات
        const { data: examsData, error: examsError } = await supabase
          .from('exams')
          .select('*')
          .order('id');

        if (examsError) {
          console.error('خطأ في جلب الامتحانات:', examsError);
          setError('خطأ في جلب الامتحانات: ' + examsError.message);
          return;
        }

        console.log('الامتحانات المجلبة:', examsData);
        setExams(examsData || []);

      } catch (err) {
        console.error('خطأ في جلب البيانات:', err);
        setError(err instanceof Error ? err.message : 'خطأ غير معروف');
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-4">تشخيص الامتحانات</h1>
        <p>جاري التحميل...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-4">تشخيص الامتحانات</h1>
        <p className="text-red-600">خطأ: {error}</p>
      </div>
    );
  }

  const examLessons = lessons.filter(l => l.content_type === 'exam');

  return (
    <div className="container mx-auto px-4 py-8" dir="rtl">
      <h1 className="text-2xl font-bold mb-4">تشخيص الامتحانات</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">إحصائيات عامة</h2>
          <ul className="space-y-2">
            <li>إجمالي الدروس: {lessons.length}</li>
            <li>دروس الامتحانات: {examLessons.length}</li>
            <li>دروس التمارين: {lessons.filter(l => l.content_type === 'exercise').length}</li>
            <li>دروس الفروض: {lessons.filter(l => l.content_type === 'homework').length}</li>
            <li>دروس الملخصات: {lessons.filter(l => l.content_type === 'summary').length}</li>
            <li>إجمالي الامتحانات في جدول exams: {exams.length}</li>
          </ul>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">دروس الامتحانات</h2>
          {examLessons.length > 0 ? (
            <ul className="space-y-2">
              {examLessons.map(lesson => (
                <li key={lesson.id} className="border-b pb-2">
                  <div className="font-medium">{lesson.title}</div>
                  <div className="text-sm text-gray-600">
                    ID: {lesson.id} | المادة: {lesson.subject_id}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-600">لا توجد دروس امتحانات</p>
          )}
        </div>
      </div>

      <div className="mt-8 bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">جدول الامتحانات</h2>
        {exams.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 px-4 py-2">ID</th>
                  <th className="border border-gray-300 px-4 py-2">العنوان</th>
                  <th className="border border-gray-300 px-4 py-2">الدرس</th>
                  <th className="border border-gray-300 px-4 py-2">التلميح</th>
                </tr>
              </thead>
              <tbody>
                {exams.map(exam => (
                  <tr key={exam.id}>
                    <td className="border border-gray-300 px-4 py-2">{exam.id}</td>
                    <td className="border border-gray-300 px-4 py-2">{exam.title || 'بدون عنوان'}</td>
                    <td className="border border-gray-300 px-4 py-2">{exam.lesson_id}</td>
                    <td className="border border-gray-300 px-4 py-2">{exam.hint || 'بدون تلميح'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-600">لا توجد امتحانات في قاعدة البيانات</p>
        )}
      </div>

      <div className="mt-8 bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">تفاصيل جميع الدروس</h2>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 px-4 py-2">العنوان</th>
                <th className="border border-gray-300 px-4 py-2">النوع</th>
                <th className="border border-gray-300 px-4 py-2">المادة</th>
              </tr>
            </thead>
            <tbody>
              {lessons.map(lesson => (
                <tr key={lesson.id}>
                  <td className="border border-gray-300 px-4 py-2">{lesson.title}</td>
                  <td className="border border-gray-300 px-4 py-2">{lesson.content_type || 'غير محدد'}</td>
                  <td className="border border-gray-300 px-4 py-2">{lesson.subject_id}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
